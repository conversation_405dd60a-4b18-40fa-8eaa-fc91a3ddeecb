<?php

namespace App\Notifications\OcMed\Ticket;

use App\Support\Helpers\DatabaseNotificationHelper;
use App\Support\Helpers\IconHelper;
use App\Support\Helpers\TenantHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TicketTransferedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly int $ticketId,
        private readonly string $ticketRoute
    ) {}

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function viaQueues(): array
    {
        return [
            'mail' => config('queue.default_queue_names.notifications'),
            'database' => config('queue.default_queue_names.notifications'),
        ];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(TenantHelper::getCompanyName() . ' - transferência de chamado')
            ->greeting("Olá, $notifiable->name!")
            ->line("O chamado #$this->ticketId foi transferido para você.")
            ->action("Acessar chamado #$this->ticketId", $this->ticketRoute)
            ->salutation('Tenha um bom dia!');
    }

    public function toArray($notifiable): array
    {
        return (new DatabaseNotificationHelper(
            notifiable: $notifiable,
            title: 'Transferência de chamado',
            icon: IconHelper::SOLID_HEADSET,
            color: 'primary',
            message: "O chamado #$this->ticketId foi transferido para você. <a href='$this->ticketRoute'>Clique aqui</a> para visualizá-lo.",
        ))->toArray();
    }
}
