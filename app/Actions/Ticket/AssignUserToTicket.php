<?php

namespace App\Actions\Ticket;

use App\Models\Ticket;
use App\Notifications\OcMed\Ticket\TicketTransferedNotification;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Throwable;

class AssignUserToTicket
{
    use AsAction;

    public function asController(ActionRequest $request, Ticket $ticket): RedirectResponse
    {
        try {
            $this->handle($ticket, $request->input('user_id'), $request->input('additional_info'));
            return redirect_success('tickets.show', __('tickets.responses.transfer.success'), $ticket->id);
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(Ticket $ticket, int $userId, string $additionalInfo): Ticket
    {
        try {
            /** @var \App\Models\Ticket $ticket */
            $ticket = DB::transaction(function () use ($ticket, $userId, $additionalInfo) {
                $ticket->ticketUserTransfers()->create([
                    'from_user_id' => $ticket->assigned_to_user_id,
                    'to_user_id' => $userId,
                    'additional_info' => $additionalInfo,
                ]);

                $ticket->update(['assigned_to_user_id' => $userId]);

                return $ticket;
            });

            try {
                $ticket->assignedUser->notify(
                    new TicketTransferedNotification($ticket->id, route('tickets.show', $ticket->id))
                );
            } catch (Throwable $th) {
                error($th);
            }

            return $ticket;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
